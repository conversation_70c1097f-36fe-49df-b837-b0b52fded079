import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class AdminGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    
    return this.authService.validateSession().pipe(
      map((isValid: boolean) => {
        if (isValid && this.authService.isAuthenticated()) {
          const currentAdmin = this.authService.getCurrentAdmin();
          
          if (currentAdmin) {
            console.log(`✅ Admin access granted for: ${currentAdmin.fullName} (${currentAdmin.role})`);
            return true;
          }
        }
        
        console.log('❌ Admin access denied - redirecting to login');
        this.router.navigate(['/adminlogin'], {
          queryParams: { returnUrl: state.url }
        });
        return false;
      }),
      catchError((error) => {
        console.error('❌ Admin guard error:', error);
        this.router.navigate(['/adminlogin'], {
          queryParams: { returnUrl: state.url }
        });
        return of(false);
      })
    );
  }
}
